<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class BannerController extends Controller
{
    /**
     * 获取首页轮播图列表
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            // 临时使用静态数据，避免数据库连接问题
            $banners = [
                [
                    'id' => 1,
                    'title' => '商品提醒系统',
                    'image_url' => 'https://picsum.photos/1200/400?random=1',
                    'link' => '#',
                    'description' => '专业的商品监控与提醒服务',
                    'target' => '_self',
                    'alt_text' => '商品提醒系统',
                    'sort_order' => 1,
                ],
                [
                    'id' => 2,
                    'title' => 'H家包包新品提醒',
                    'image_url' => 'https://picsum.photos/1200/400?random=2',
                    'link' => '/products',
                    'description' => '支持30多种包包类型的新品到货提醒',
                    'target' => '_self',
                    'alt_text' => 'H家包包新品提醒',
                    'sort_order' => 2,
                ],
                [
                    'id' => 3,
                    'title' => '全球16个国家地区支持',
                    'image_url' => 'https://picsum.photos/1200/400?random=3',
                    'link' => '/subscribe',
                    'description' => '美国、英国、法国、德国、日本等16个国家和地区',
                    'target' => '_self',
                    'alt_text' => '全球支持',
                    'sort_order' => 3,
                ],
            ];

            return response()->json([
                'success' => true,
                'message' => '获取轮播图成功',
                'data' => $banners
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取轮播图失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 获取单个轮播图详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $banner = Banner::valid()
                ->select([
                    'id',
                    'title',
                    'image',
                    'link',
                    'description',
                    'target',
                    'alt_text',
                    'sort_order',
                    'start_time',
                    'end_time'
                ])
                ->find($id);

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => '轮播图不存在或已失效',
                    'data' => null
                ], 404);
            }

            $bannerData = [
                'id' => $banner->id,
                'title' => $banner->title,
                'image_url' => $banner->image_url,
                'link' => $banner->link,
                'description' => $banner->description,
                'target' => $banner->target,
                'alt_text' => $banner->alt_text ?: $banner->title,
                'sort_order' => $banner->sort_order,
                'start_time' => $banner->start_time?->format('Y-m-d H:i:s'),
                'end_time' => $banner->end_time?->format('Y-m-d H:i:s'),
            ];

            return response()->json([
                'success' => true,
                'message' => '获取轮播图详情成功',
                'data' => $bannerData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取轮播图详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取轮播图统计信息
     *
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = [
                'total' => Banner::count(),
                'active' => Banner::active()->count(),
                'valid' => Banner::valid()->count(),
                'expired' => Banner::where('is_active', true)
                    ->where('end_time', '<', now())
                    ->count(),
                'scheduled' => Banner::where('is_active', true)
                    ->where('start_time', '>', now())
                    ->count(),
            ];

            return response()->json([
                'success' => true,
                'message' => '获取轮播图统计成功',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取轮播图统计失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 创建轮播图
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'image' => 'required|url|max:500',
                'link' => 'nullable|url|max:500',
                'description' => 'nullable|string|max:1000',
                'target' => 'nullable|in:_self,_blank',
                'alt_text' => 'nullable|string|max:255',
                'sort_order' => 'nullable|integer|min:0',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after:start_time',
                'is_active' => 'boolean',
            ], [
                'title.required' => '轮播图标题不能为空',
                'image.required' => '轮播图图片不能为空',
                'image.url' => '请输入有效的图片URL',
                'link.url' => '请输入有效的链接URL',
                'end_time.after' => '结束时间必须晚于开始时间',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 400);
            }

            $bannerData = [
                'title' => $request->title,
                'image' => $request->image,
                'link' => $request->link,
                'description' => $request->description,
                'target' => $request->target ?: '_self',
                'alt_text' => $request->alt_text ?: $request->title,
                'sort_order' => $request->sort_order ?: 0,
                'start_time' => $request->start_time ? now()->parse($request->start_time) : null,
                'end_time' => $request->end_time ? now()->parse($request->end_time) : null,
                'is_active' => $request->is_active ?? true,
            ];

            $banner = Banner::create($bannerData);

            Log::info('轮播图创建成功', [
                'banner_id' => $banner->id,
                'title' => $banner->title,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => '轮播图创建成功',
                'data' => [
                    'id' => $banner->id,
                    'title' => $banner->title,
                    'image_url' => $banner->image_url,
                    'link' => $banner->link,
                    'description' => $banner->description,
                    'target' => $banner->target,
                    'alt_text' => $banner->alt_text,
                    'sort_order' => $banner->sort_order,
                    'is_active' => $banner->is_active,
                    'start_time' => $banner->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $banner->end_time?->format('Y-m-d H:i:s'),
                    'created_at' => $banner->created_at->format('Y-m-d H:i:s'),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('轮播图创建失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '轮播图创建失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新轮播图
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $banner = Banner::find($id);

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => '轮播图不存在',
                    'data' => null
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'sometimes|required|string|max:255',
                'image' => 'sometimes|required|url|max:500',
                'link' => 'nullable|url|max:500',
                'description' => 'nullable|string|max:1000',
                'target' => 'nullable|in:_self,_blank',
                'alt_text' => 'nullable|string|max:255',
                'sort_order' => 'nullable|integer|min:0',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after:start_time',
                'is_active' => 'boolean',
            ], [
                'title.required' => '轮播图标题不能为空',
                'image.required' => '轮播图图片不能为空',
                'image.url' => '请输入有效的图片URL',
                'link.url' => '请输入有效的链接URL',
                'end_time.after' => '结束时间必须晚于开始时间',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 400);
            }

            $updateData = [];

            if ($request->has('title')) $updateData['title'] = $request->title;
            if ($request->has('image')) $updateData['image'] = $request->image;
            if ($request->has('link')) $updateData['link'] = $request->link;
            if ($request->has('description')) $updateData['description'] = $request->description;
            if ($request->has('target')) $updateData['target'] = $request->target;
            if ($request->has('alt_text')) $updateData['alt_text'] = $request->alt_text;
            if ($request->has('sort_order')) $updateData['sort_order'] = $request->sort_order;
            if ($request->has('start_time')) $updateData['start_time'] = $request->start_time ? now()->parse($request->start_time) : null;
            if ($request->has('end_time')) $updateData['end_time'] = $request->end_time ? now()->parse($request->end_time) : null;
            if ($request->has('is_active')) $updateData['is_active'] = $request->is_active;

            $banner->update($updateData);

            Log::info('轮播图更新成功', [
                'banner_id' => $banner->id,
                'title' => $banner->title,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => '轮播图更新成功',
                'data' => [
                    'id' => $banner->id,
                    'title' => $banner->title,
                    'image_url' => $banner->image_url,
                    'link' => $banner->link,
                    'description' => $banner->description,
                    'target' => $banner->target,
                    'alt_text' => $banner->alt_text,
                    'sort_order' => $banner->sort_order,
                    'is_active' => $banner->is_active,
                    'start_time' => $banner->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $banner->end_time?->format('Y-m-d H:i:s'),
                    'updated_at' => $banner->updated_at->format('Y-m-d H:i:s'),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('轮播图更新失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '轮播图更新失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除轮播图
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $banner = Banner::find($id);

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => '轮播图不存在',
                    'data' => null
                ], 404);
            }

            $banner->delete();

            Log::info('轮播图删除成功', [
                'banner_id' => $id,
                'title' => $banner->title,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => '轮播图删除成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error('轮播图删除失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '轮播图删除失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 切换轮播图状态
     *
     * @param int $id
     * @return JsonResponse
     */
    public function toggle(int $id): JsonResponse
    {
        try {
            $banner = Banner::find($id);

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => '轮播图不存在',
                    'data' => null
                ], 404);
            }

            $banner->update(['is_active' => !$banner->is_active]);

            Log::info('轮播图状态切换成功', [
                'banner_id' => $id,
                'title' => $banner->title,
                'new_status' => $banner->is_active,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => '轮播图状态切换成功',
                'data' => [
                    'id' => $banner->id,
                    'is_active' => $banner->is_active,
                    'status_text' => $banner->status_text,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('轮播图状态切换失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '轮播图状态切换失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
