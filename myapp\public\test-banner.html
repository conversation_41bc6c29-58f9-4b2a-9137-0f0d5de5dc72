<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .banner-container {
            position: relative;
            height: 400px;
            overflow: hidden;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .banner-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        .banner-slide.active {
            opacity: 1;
        }
        .banner-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .banner-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            color: white;
            padding: 20px;
        }
        .banner-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .banner-description {
            font-size: 16px;
            opacity: 0.9;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        .btn {
            background: #ff8c42;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
        }
        .btn:hover {
            background: #e67e22;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>轮播图测试页面</h1>
        
        <div class="banner-container" id="bannerContainer">
            <div class="banner-slide active">
                <div style="background: linear-gradient(45deg, #ff8c42, #e67e22); height: 100%; display: flex; align-items: center; justify-content: center;">
                    <div style="text-align: center; color: white;">
                        <h2>加载中...</h2>
                        <p>正在获取轮播图数据</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="loadBanners()">重新加载轮播图</button>
            <button class="btn" onclick="prevSlide()">上一张</button>
            <button class="btn" onclick="nextSlide()">下一张</button>
            <button class="btn" onclick="toggleAutoPlay()">切换自动播放</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        let banners = [];
        let currentSlide = 0;
        let autoPlayInterval = null;
        let isAutoPlay = true;

        // 加载轮播图数据
        async function loadBanners() {
            try {
                showStatus('正在加载轮播图...', 'success');
                
                const response = await fetch('/api/banners', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('API响应:', data);
                    
                    if (data.success && data.data && data.data.length > 0) {
                        banners = data.data;
                        renderBanners();
                        startAutoPlay();
                        showStatus(`成功加载 ${banners.length} 张轮播图`, 'success');
                    } else {
                        showStatus('没有找到轮播图数据', 'error');
                        showDefault();
                    }
                } else {
                    showStatus(`请求失败: ${response.status} ${response.statusText}`, 'error');
                    showDefault();
                }
                
            } catch (error) {
                console.error('加载轮播图失败:', error);
                showStatus(`加载失败: ${error.message}`, 'error');
                showDefault();
            }
        }

        // 渲染轮播图
        function renderBanners() {
            const container = document.getElementById('bannerContainer');
            container.innerHTML = '';
            
            banners.forEach((banner, index) => {
                const slide = document.createElement('div');
                slide.className = `banner-slide ${index === 0 ? 'active' : ''}`;
                slide.style.cursor = banner.link && banner.link !== '#' ? 'pointer' : 'default';
                
                slide.innerHTML = `
                    <img src="${banner.image_url}" 
                         alt="${banner.alt_text || banner.title}" 
                         onerror="this.parentElement.innerHTML='<div style=\\"background: linear-gradient(45deg, #ff8c42, #e67e22); height: 100%; display: flex; align-items: center; justify-content: center; color: white;\\"><div style=\\"text-align: center;\\"><h2>${banner.title}</h2><p>${banner.description || ''}</p></div></div>'"
                         />
                    ${banner.title || banner.description ? `
                        <div class="banner-info">
                            ${banner.title ? `<div class="banner-title">${banner.title}</div>` : ''}
                            ${banner.description ? `<div class="banner-description">${banner.description}</div>` : ''}
                        </div>
                    ` : ''}
                `;
                
                // 添加点击事件
                if (banner.link && banner.link !== '#' && banner.link !== '') {
                    slide.addEventListener('click', () => {
                        if (banner.target === '_blank') {
                            window.open(banner.link, '_blank');
                        } else {
                            window.location.href = banner.link;
                        }
                    });
                }
                
                container.appendChild(slide);
            });
        }

        // 显示默认内容
        function showDefault() {
            const container = document.getElementById('bannerContainer');
            container.innerHTML = `
                <div class="banner-slide active">
                    <div style="background: linear-gradient(45deg, #ff8c42, #e67e22); height: 100%; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center; color: white;">
                            <h2>商品提醒系统</h2>
                            <p>Product Alert System</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示状态信息
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        // 切换到指定幻灯片
        function showSlide(index) {
            const slides = document.querySelectorAll('.banner-slide');
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        }

        // 下一张
        function nextSlide() {
            if (banners.length === 0) return;
            currentSlide = (currentSlide + 1) % banners.length;
            showSlide(currentSlide);
        }

        // 上一张
        function prevSlide() {
            if (banners.length === 0) return;
            currentSlide = (currentSlide - 1 + banners.length) % banners.length;
            showSlide(currentSlide);
        }

        // 开始自动播放
        function startAutoPlay() {
            if (banners.length <= 1 || !isAutoPlay) return;
            
            stopAutoPlay();
            autoPlayInterval = setInterval(() => {
                nextSlide();
            }, 5000);
        }

        // 停止自动播放
        function stopAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
        }

        // 切换自动播放
        function toggleAutoPlay() {
            isAutoPlay = !isAutoPlay;
            if (isAutoPlay) {
                startAutoPlay();
                showStatus('自动播放已开启', 'success');
            } else {
                stopAutoPlay();
                showStatus('自动播放已关闭', 'success');
            }
        }

        // 页面加载完成后自动加载轮播图
        document.addEventListener('DOMContentLoaded', function() {
            loadBanners();
        });
    </script>
</body>
</html>
