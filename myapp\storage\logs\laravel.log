[2025-07-11 06:09:30] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:09:33] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:09:36] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:09:39] local.WARNING: 用户登录失败 {"email":"<EMAIL>","ip":"127.0.0.1"} 
[2025-07-11 06:09:40] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:09:59] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:09:59] local.WARNING: 用户登录失败 {"email":"<EMAIL>","ip":"127.0.0.1"} 
[2025-07-11 06:09:59] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:20] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:23] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:27] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:27] local.INFO: 用户登录成功（调试模式：已跳过密码验证） {"user_id":4,"email":"<EMAIL>","role":"R_USER","ip":"127.0.0.1"} 
[2025-07-11 06:17:28] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:28] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-07-11 06:17:36] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:39] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:39] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 06:17:39","products_count":3,"filters":[]} 
[2025-07-11 06:17:43] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:44] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:47] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:55] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:55] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 06:17:55","products_count":3,"filters":[]} 
[2025-07-11 06:17:57] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:17:57] local.INFO: 用户访问商品详情页面 {"user_id":4,"product_id":"3","product_name":"iPad Air","timestamp":"2025-07-11 06:17:57"} 
[2025-07-11 06:18:01] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 06:35:27] local.INFO: 登录尝试 {"loginField":"Super","request_data":{"userName":"Super","password":"123456"}} 
[2025-07-11 06:35:27] local.WARNING: 用户未找到 {"loginField":"Super","查询条件":"状态为active的用户"} 
[2025-07-11 06:35:36] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 06:35:36] local.INFO: 找到用户 {"user_id":2,"user_name":"Admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 06:35:36] local.INFO: 密码验证结果 {"user_id":2,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 06:35:36] local.INFO: 用户登录成功 {"user_id":2,"email":"<EMAIL>","name":"Admin","login_time":"2025-07-11 06:35:36","ip":"127.0.0.1"} 
[2025-07-11 06:45:59] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 06:45:59] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 06:45:59] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 06:45:59] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 06:45:59","ip":"127.0.0.1"} 
[2025-07-11 06:47:11] local.INFO: 登录尝试 {"loginField":"Super","request_data":{"userName":"Super","password":"123456"}} 
[2025-07-11 06:47:11] local.WARNING: 用户未找到 {"loginField":"Super","查询条件":"状态为active的用户"} 
[2025-07-11 06:47:17] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 06:47:17] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 06:47:17] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 06:47:17] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 06:47:17","ip":"127.0.0.1"} 
[2025-07-11 06:52:26] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 06:52:26] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 06:52:26] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 06:52:26] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 06:52:26","ip":"127.0.0.1"} 
[2025-07-11 07:03:39] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:03:40] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 07:03:40] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 07:03:40] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 07:03:40","ip":"127.0.0.1"} 
[2025-07-11 07:03:43] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:03:43] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 07:03:43] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 07:03:43] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 07:03:43","ip":"127.0.0.1"} 
[2025-07-11 07:04:54] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:05:22] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:05:24] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:05:38] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:05:38] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-07-11 07:05:38] local.ERROR: 订阅处理失败 {"user_id":4,"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'region_id' in 'field list' (SQL: insert into `payment_orders` (`user_id`, `plan_id`, `region_id`, `amount`, `original_amount`, `discount_amount`, `currency`, `payment_method`, `billing_cycle`, `status`, `order_number`, `expires_at`, `updated_at`, `created_at`) values (4, 1, 1, 35.964, 119.88, 83.916, USD, card, yearly, pending, SUB202507110705382448, 2025-07-11 07:35:38, 2025-07-11 07:05:38, 2025-07-11 07:05:38))","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3322): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1869): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1330): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1295): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(986): Illuminate\\Database\\Eloquent\\Model->save()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(319): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(987): tap()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#15 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\SubscriptionController.php(95): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\SubscriptionController->processSubscription()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#22 G:\\php\\laravel\\myapp\\app\\Http\\Middleware\\CheckUserRole.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\CheckUserRole->handle()
#24 G:\\php\\laravel\\myapp\\app\\Http\\Middleware\\SetLanguage.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetLanguage->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#58 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#66 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#67 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#68 {main}"} 
[2025-07-11 07:05:38] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:26] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:28] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:31] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:31] local.INFO: 用户登录成功（调试模式：已跳过密码验证） {"user_id":4,"email":"<EMAIL>","role":"R_USER","ip":"127.0.0.1"} 
[2025-07-11 07:07:31] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:31] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-07-11 07:07:36] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:38] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:39] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:40] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 07:07:40","products_count":3,"filters":[]} 
[2025-07-11 07:07:52] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:07:52] local.INFO: 用户访问商品详情页面 {"user_id":4,"product_id":"1","product_name":"iPhone 15 Pro","timestamp":"2025-07-11 07:07:52"} 
[2025-07-11 07:08:07] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:09] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:09] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 07:08:09","products_count":3,"filters":[]} 
[2025-07-11 07:08:18] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:18] local.INFO: 用户访问商品详情页面 {"user_id":4,"product_id":"1","product_name":"iPhone 15 Pro","timestamp":"2025-07-11 07:08:18"} 
[2025-07-11 07:08:21] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:21] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-07-11 07:08:21] local.INFO: 用户关注商品 {"user_id":4,"product_id":"1","product_name":"iPhone 15 Pro","settings":{"email_notifications":true,"wechat_notifications":false},"timestamp":"2025-07-11 07:08:21"} 
[2025-07-11 07:08:23] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:23] local.INFO: 用户访问商品详情页面 {"user_id":4,"product_id":"1","product_name":"iPhone 15 Pro","timestamp":"2025-07-11 07:08:23"} 
[2025-07-11 07:08:41] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:42] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:42] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 07:08:42","products_count":3,"filters":[]} 
[2025-07-11 07:08:47] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:47] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-07-11 07:08:48] local.INFO: 用户关注商品 {"user_id":4,"product_id":"2","product_name":"MacBook Pro 14","settings":{"email_notifications":true,"wechat_notifications":false},"timestamp":"2025-07-11 07:08:48"} 
[2025-07-11 07:08:49] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:08:49] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 07:08:49","products_count":3,"filters":[]} 
[2025-07-11 07:09:26] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:09:26] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 07:09:26] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 07:09:26] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 07:09:26","ip":"127.0.0.1"} 
[2025-07-11 07:09:43] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:09:43] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-07-11 07:09:49] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:09:51] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:09:55] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:11:45] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:11:47] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:11:56] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:11:57] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:11:58] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 07:11:58","products_count":3,"filters":[]} 
[2025-07-11 07:13:04] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:13:04] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 07:13:04] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 07:13:04] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 07:13:04","ip":"127.0.0.1"} 
[2025-07-11 07:13:40] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:14:22] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:14:23] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-07-11 07:14:23","products_count":3,"filters":[]} 
[2025-07-11 07:15:06] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:15:06] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 07:15:06] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 07:15:06] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 07:15:06","ip":"127.0.0.1"} 
[2025-07-11 07:19:09] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:19:14] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:23:08] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:23:12] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:23:12] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-07-11 07:26:28] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 07:37:28] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select * from `personal_access_tokens` where `personal_access_tokens`.`id` = 31 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select * from `personal_access_tokens` where `personal_access_tokens`.`id` = 31 limit 1) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(442): Illuminate\\Database\\Eloquent\\Builder->first()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(66): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#15 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#28 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#47 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#48 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(442): Illuminate\\Database\\Eloquent\\Builder->first()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->find()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(66): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#25 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#56 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 {main}
"} 
[2025-07-11 07:37:45] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:37:45] local.ERROR: 登录过程发生错误 {"error":"SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select * from `users` where (`email` = <EMAIL> or `name` = <EMAIL>) and `status` = 1 limit 1)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get()
#9 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Builder->first()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#29 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:37:46] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:37:46] local.ERROR: 登录过程发生错误 {"error":"SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select * from `users` where (`email` = <EMAIL> or `name` = <EMAIL>) and `status` = 1 limit 1)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get()
#9 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Builder->first()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#29 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:37:48] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:37:48] local.ERROR: 登录过程发生错误 {"error":"SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select * from `users` where (`email` = <EMAIL> or `name` = <EMAIL>) and `status` = 1 limit 1)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(296): Illuminate\\Database\\Eloquent\\Builder->get()
#9 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Builder->first()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#29 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 07:41:25] local.ERROR: 获取商品统计失败 {"error":"SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (SQL: select count(*) as aggregate from `products`)"} 
[2025-07-11 07:43:00] local.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = laravel_app and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = laravel_app and table_name = migrations and table_type = 'BASE TABLE') at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(44): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(722): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(80): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#15 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'root', 'root', Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getPdo()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(44): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(722): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(80): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#24 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-07-11 08:01:37] local.ERROR: Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#19 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider()
#27 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#34 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#35 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect()
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#24 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider()
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#39 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#40 {main}
"} 
[2025-07-11 08:08:55] local.ERROR: Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#19 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider()
#27 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#34 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#35 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect()
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#24 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider()
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#39 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#40 {main}
"} 
[2025-07-11 08:09:03] local.ERROR: Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'laravel_app', '', Array)
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'laravel_app', '', Array)
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build(Object(Closure))
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve('db.schema', Array, true)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('db.schema', Array)
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make('db.schema', Array)
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make('db.schema')
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet('db.schema')
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('db.schema')
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#19 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('defaultStringLe...', Array)
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call(Array)
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#27 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 30)
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk(Array, Object(Closure))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#33 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [laravel_app] does not exist. Ensure this is an absolute path to the database. at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'laravel_app', '', Array)
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'laravel_app', '', Array)
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build(Object(Closure))
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve('db.schema', Array, true)
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('db.schema', Array)
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make('db.schema', Array)
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make('db.schema')
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet('db.schema')
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('db.schema')
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#24 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('defaultStringLe...', Array)
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call(Array)
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 30)
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk(Array, Object(Closure))
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#38 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}
"} 
[2025-07-11 08:09:20] local.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = laravel_app and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = laravel_app and table_name = migrations and table_type = 'BASE TABLE') at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(44): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(722): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(80): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#15 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'root', 'root', Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root', Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getPdo()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(44): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(722): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(80): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#24 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-07-11 08:09:43] local.ERROR: could not find driver (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'database/larave...', '', Array)
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'database/larave...', '', Array)
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build(Object(Closure))
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve('db.schema', Array, true)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('db.schema', Array)
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make('db.schema', Array)
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make('db.schema')
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet('db.schema')
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('db.schema')
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#19 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('defaultStringLe...', Array)
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call(Array)
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#27 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 30)
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk(Array, Object(Closure))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#33 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('sqlite:G:\\\\php\\\\l...', NULL, NULL, Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:G:\\\\php\\\\l...', NULL, NULL, Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:G:\\\\php\\\\l...', Array, Array)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'database/larave...', '', Array)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'database/larave...', '', Array)
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build(Object(Closure))
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve('db.schema', Array, true)
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve('db.schema', Array)
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make('db.schema', Array)
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make('db.schema')
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet('db.schema')
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('db.schema')
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#27 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('defaultStringLe...', Array)
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call(Array)
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#35 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 30)
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk(Array, Object(Closure))
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#41 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 {main}
"} 
[2025-07-11 08:10:27] local.ERROR: Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#19 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider()
#27 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#34 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#35 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect()
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(885): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(770): Illuminate\\Container\\Container->build()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(881): Illuminate\\Container\\Container->resolve()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(706): Illuminate\\Foundation\\Application->resolve()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(866): Illuminate\\Container\\Container->make()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Foundation\\Application->make()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(225): Illuminate\\Container\\Container->offsetGet()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(193): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(332): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#24 G:\\php\\laravel\\myapp\\app\\Providers\\AppServiceProvider.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(954): Illuminate\\Container\\Container->call()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(935): Illuminate\\Foundation\\Application->bootProvider()
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(936): array_walk()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(265): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Foundation\\Application->bootstrapWith()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(160): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#39 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#40 {main}
"} 
[2025-07-11 08:17:50] local.INFO: 登录尝试 {"loginField":"super@example","request_data":{"userName":"super@example","password":"123456"}} 
[2025-07-11 08:17:50] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"super@example","password":"123456"}} 
[2025-07-11 08:17:52] local.INFO: 登录尝试 {"loginField":"super@example","request_data":{"userName":"super@example","password":"123456"}} 
[2025-07-11 08:17:52] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"super@example","password":"123456"}} 
[2025-07-11 08:17:54] local.INFO: 登录尝试 {"loginField":"super@example","request_data":{"userName":"super@example","password":"123456"}} 
[2025-07-11 08:17:54] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"super@example","password":"123456"}} 
[2025-07-11 08:17:58] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:17:58] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:00] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:00] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:01] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:01] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:19] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:19] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:21] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:21] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:22] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:18:22] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:00] local.INFO: 当前语言环境设置为: zh_CN  
[2025-07-11 08:19:13] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:13] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:15] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:15] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:16] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:16] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:23] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:23] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:24] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:24] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:26] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:19:26] local.ERROR: 登录过程发生错误 {"error":"Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;)","trace":"#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\app\\Http\\Controllers\\Api\\AuthController.php(57): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\AuthController->login()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(102): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(54): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:44:23] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"password":"123456","userName":"<EMAIL>"}} 
[2025-07-11 08:44:23] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 08:44:23] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 08:44:23] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 08:44:23","ip":"127.0.0.1"} 
[2025-07-11 08:44:53] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:44:53] local.INFO: 找到用户 {"user_id":1,"user_name":"admin","user_email":"<EMAIL>","user_status":1,"password_length":60} 
[2025-07-11 08:44:53] local.INFO: 密码验证结果 {"user_id":1,"password_check":true,"provided_password_length":6,"stored_password_length":60} 
[2025-07-11 08:44:53] local.INFO: 用户登录成功 {"user_id":1,"email":"<EMAIL>","name":"admin","login_time":"2025-07-11 08:44:53","ip":"127.0.0.1"} 
[2025-07-11 08:44:53] local.ERROR: Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(61): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#19 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect()
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(61): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#24 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}
"} 
[2025-07-11 08:44:55] local.ERROR: Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(61): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#19 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect()
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(61): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#24 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}
"} 
[2025-07-11 08:44:56] local.ERROR: Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(61): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#19 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [database/laravel_app.sqlite] does not exist. Ensure this is an absolute path to the database. at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect()
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func()
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1815): Illuminate\\Database\\DatabaseManager->connection()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1781): Illuminate\\Database\\Eloquent\\Model::resolveConnection()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1573): Illuminate\\Database\\Eloquent\\Model->getConnection()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1492): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1528): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1481): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2330): Illuminate\\Database\\Eloquent\\Model->newQuery()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2342): Illuminate\\Database\\Eloquent\\Model->__call()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php(61): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\sanctum\\src\\Guard.php(67): Laravel\\Sanctum\\PersonalAccessToken::findToken()
#24 [internal function]: Laravel\\Sanctum\\Guard->__invoke()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\RequestGuard.php(58): call_user_func()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\RequestGuard->user()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\RequestGuard->check()
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate()
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#37 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle()
#39 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#42 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#47 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#49 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#51 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#53 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#55 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 {main}
"} 
[2025-07-11 08:46:51] local.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = laravel_app and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = laravel_app and table_name = migrations and table_type = 'BASE TABLE') at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(44): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(722): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(80): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#15 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'root', 'root123', Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root123', Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(486): Illuminate\\Database\\Connection->getPdo()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(44): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('migrations')
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(722): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(636): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(80): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#24 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-07-11 08:50:21] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"password":"123456","userName":"<EMAIL>"}} 
[2025-07-11 08:50:21] local.INFO: 用户登录成功 {"loginField":"<EMAIL>","role":"R_SUPER","name":"Super Admin","login_time":"2025-07-11 08:50:21","ip":"127.0.0.1"} 
[2025-07-11 08:50:27] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:50:27] local.INFO: 用户登录成功 {"loginField":"<EMAIL>","role":"R_SUPER","name":"Super Admin","login_time":"2025-07-11 08:50:27","ip":"127.0.0.1"} 
[2025-07-11 08:51:32] local.INFO: 登录尝试 {"loginField":"<EMAIL>","request_data":{"userName":"<EMAIL>","password":"123456"}} 
[2025-07-11 08:51:32] local.INFO: 用户登录成功 {"loginField":"<EMAIL>","role":"R_SUPER","name":"Super Admin","login_time":"2025-07-11 08:51:32","ip":"127.0.0.1"} 
[2025-08-06 02:01:04] local.INFO: 当前语言环境设置为: zh_CN  
[2025-08-06 02:05:41] local.INFO: 当前语言环境设置为: zh_CN  
[2025-08-06 02:05:41] local.INFO: 语言切换成功，切换到: en  
[2025-08-06 02:05:41] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:25] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:35] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:41] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:41] local.INFO: 用户登录成功（调试模式：已跳过密码验证） {"user_id":4,"email":"<EMAIL>","role":"R_USER","ip":"127.0.0.1"} 
[2025-08-06 02:06:42] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:42] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-08-06 02:06:48] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:50] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:55] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:57] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:06:57] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-08-06 02:06:57","products_count":3,"filters":[]} 
[2025-08-06 02:07:01] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:07:01] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-08-06 02:07:07] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:14:31] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:14:31] local.INFO: CheckUserRole: 用户角色验证通过，用户角色：R_USER  
[2025-08-06 02:26:23] local.ERROR: could not find driver (SQL: insert into `banners` (`alt_text`, `created_at`, `description`, `end_time`, `image`, `is_active`, `link`, `sort_order`, `start_time`, `target`, `title`, `updated_at`) values (Art Design Pro Banner, 2025-09-12 02:26:23, Art Design Pro - 一个美观实用的Vue3管理后台模板, ?, https://picsum.photos/1200/400?random=1, 1, https://www.lingchen.kim/art-design-pro, 1, ?, _blank, 欢迎使用Art Design Pro, 2025-09-12 02:26:23), (功能介绍, 2025-09-12 02:26:23, 包含用户管理、权限控制、数据统计等完整功能, ?, https://picsum.photos/1200/400?random=2, 1, #, 2, ?, _self, 功能丰富的管理系统, 2025-09-12 02:26:23), (响应式设计, 2025-09-12 02:26:23, 完美适配各种设备尺寸，提供最佳用户体验, ?, https://picsum.photos/1200/400?random=3, 1, #, 3, ?, _self, 响应式设计, 2025-09-12 02:26:23), (新功能预告, 2025-09-12 02:26:23, 更多精彩功能正在开发中，敬请期待, 2025-09-12 02:26:23, https://picsum.photos/1200/400?random=4, 0, #, 4, 2025-09-12 02:26:23, _self, 即将推出新功能, 2025-09-12 02:26:23)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: insert into `banners` (`alt_text`, `created_at`, `description`, `end_time`, `image`, `is_active`, `link`, `sort_order`, `start_time`, `target`, `title`, `updated_at`) values (Art Design Pro Banner, 2025-09-12 02:26:23, Art Design Pro - 一个美观实用的Vue3管理后台模板, ?, https://picsum.photos/1200/400?random=1, 1, https://www.lingchen.kim/art-design-pro, 1, ?, _blank, 欢迎使用Art Design Pro, 2025-09-12 02:26:23), (功能介绍, 2025-09-12 02:26:23, 包含用户管理、权限控制、数据统计等完整功能, ?, https://picsum.photos/1200/400?random=2, 1, #, 2, ?, _self, 功能丰富的管理系统, 2025-09-12 02:26:23), (响应式设计, 2025-09-12 02:26:23, 完美适配各种设备尺寸，提供最佳用户体验, ?, https://picsum.photos/1200/400?random=3, 1, #, 3, ?, _self, 响应式设计, 2025-09-12 02:26:23), (新功能预告, 2025-09-12 02:26:23, 更多精彩功能正在开发中，敬请期待, 2025-09-12 02:26:23, https://picsum.photos/1200/400?random=4, 0, #, 4, 2025-09-12 02:26:23, _self, 即将推出新功能, 2025-09-12 02:26:23)) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('insert into `ba...', Array, Object(Closure))
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('insert into `ba...', Array, Object(Closure))
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement('insert into `ba...', Array)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3274): Illuminate\\Database\\Connection->insert('insert into `ba...', Array)
#4 G:\\php\\laravel\\myapp\\database\\seeders\\BannerSeeder.php(77): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\BannerSeeder->run()
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(81): Illuminate\\Database\\Seeder->__invoke()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(82): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#21 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'root', 'root123', Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', 'root123', Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `ba...', Array)
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('insert into `ba...', Array, Object(Closure))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('insert into `ba...', Array, Object(Closure))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(498): Illuminate\\Database\\Connection->statement('insert into `ba...', Array)
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3274): Illuminate\\Database\\Connection->insert('insert into `ba...', Array)
#12 G:\\php\\laravel\\myapp\\database\\seeders\\BannerSeeder.php(77): Illuminate\\Database\\Query\\Builder->insert(Array)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\BannerSeeder->run()
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(81): Illuminate\\Database\\Seeder->__invoke()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(82): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#29 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 {main}
"} 
[2025-08-06 02:26:53] local.ERROR: could not find driver (SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: PRAGMA foreign_keys = ON;) at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'database/larave...', '', Array)
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'database/larave...', '', Array)
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\WipeCommand.php(80): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\WipeCommand.php(59): Illuminate\\Database\\Console\\WipeCommand->dropAllTables(NULL)
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\WipeCommand->handle()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#17 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(40): Illuminate\\Console\\Command->runCommand('db:wipe', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(45): Illuminate\\Console\\Command->callSilent('db:wipe', Array)
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render('Dropping all ta...', Object(Closure))
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(49): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#31 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('sqlite:G:\\\\php\\\\l...', NULL, NULL, Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:G:\\\\php\\\\l...', NULL, NULL, Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:G:\\\\php\\\\l...', Array, Array)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1181): call_user_func(Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(539): Illuminate\\Database\\Connection->getPdo()
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(398): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(35): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'database/larave...', '', Array)
#13 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'database/larave...', '', Array)
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(152): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\WipeCommand.php(80): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\WipeCommand.php(59): Illuminate\\Database\\Console\\WipeCommand->dropAllTables(NULL)
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\WipeCommand->handle()
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#25 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(40): Illuminate\\Console\\Command->runCommand('db:wipe', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(45): Illuminate\\Console\\Command->callSilent('db:wipe', Array)
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render('Dropping all ta...', Object(Closure))
#32 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(49): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#39 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Command\\Command.php(291): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(1014): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 G:\\php\\laravel\\myapp\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 G:\\php\\laravel\\myapp\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 {main}
"} 
[2025-08-06 02:31:48] local.ERROR: Call to undefined function Illuminate\Encryption\openssl_cipher_iv_length() {"exception":"[object] (Error(code: 0): Call to undefined function Illuminate\\Encryption\\openssl_cipher_iv_length() at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php:246)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php(213): Illuminate\\Encryption\\Encrypter->validPayload(Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php(151): Illuminate\\Encryption\\Encrypter->getJsonPayload(Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(138): Illuminate\\Encryption\\Encrypter->decrypt('eyJpdiI6ImlDOVJ...', false)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(84): Illuminate\\Cookie\\Middleware\\EncryptCookies->decryptCookie('XSRF-TOKEN', 'eyJpdiI6ImlDOVJ...')
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Cookie\\Middleware\\EncryptCookies->decrypt(Object(Illuminate\\Http\\Request))
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('G:\\\\php\\\\laravel\\\\...')
#34 {main}
"} 
[2025-08-06 02:33:11] local.ERROR: Call to undefined function Illuminate\Encryption\openssl_cipher_iv_length() {"exception":"[object] (Error(code: 0): Call to undefined function Illuminate\\Encryption\\openssl_cipher_iv_length() at G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php:246)
[stacktrace]
#0 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php(213): Illuminate\\Encryption\\Encrypter->validPayload(Array)
#1 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php(151): Illuminate\\Encryption\\Encrypter->getJsonPayload(Array)
#2 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(138): Illuminate\\Encryption\\Encrypter->decrypt('eyJpdiI6ImlDOVJ...', false)
#3 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(84): Illuminate\\Cookie\\Middleware\\EncryptCookies->decryptCookie('XSRF-TOKEN', 'eyJpdiI6ImlDOVJ...')
#4 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Cookie\\Middleware\\EncryptCookies->decrypt(Object(Illuminate\\Http\\Request))
#5 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 G:\\php\\laravel\\myapp\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 G:\\php\\laravel\\myapp\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 G:\\php\\laravel\\myapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('G:\\\\php\\\\laravel\\\\...')
#34 {main}
"} 
[2025-08-06 02:33:20] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:33:34] local.INFO: 当前语言环境设置为: en  
[2025-08-06 02:33:34] local.INFO: 用户访问商品浏览页面 {"user_id":4,"timestamp":"2025-08-06 02:33:34","products_count":3,"filters":[]} 
