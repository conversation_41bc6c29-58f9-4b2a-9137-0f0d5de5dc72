<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'darkMode' => false,
    'maxHeight' => null,
    'offset' => 8,
    'placement' => null,
    'shift' => false,
    'teleport' => false,
    'trigger' => null,
    'width' => null,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'darkMode' => false,
    'maxHeight' => null,
    'offset' => 8,
    'placement' => null,
    'shift' => false,
    'teleport' => false,
    'trigger' => null,
    'width' => null,
]); ?>
<?php foreach (array_filter(([
    'darkMode' => false,
    'maxHeight' => null,
    'offset' => 8,
    'placement' => null,
    'shift' => false,
    'teleport' => false,
    'trigger' => null,
    'width' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div
    <?php echo e($attributes->class(['filament-dropdown'])); ?>

    x-data="{
        toggle: function (event) {
            $refs.panel.toggle(event)
        },
        open: function (event) {
            $refs.panel.open(event)
        },
        close: function (event) {
            $refs.panel.close(event)
        },
    }"
>
    <div
        x-on:click="toggle"
        <?php echo e($trigger->attributes->class(['filament-dropdown-trigger cursor-pointer'])); ?>

    >
        <?php echo e($trigger); ?>

    </div>

    <div
        x-ref="panel"
        x-float<?php echo e($placement ? ".placement.{$placement}" : ''); ?>.flip<?php echo e($shift ? '.shift' : ''); ?><?php echo e($teleport ? '.teleport' : ''); ?><?php echo e($offset ? '.offset' : ''); ?>="{ offset: <?php echo e($offset); ?> }"
        x-cloak
        x-transition:enter-start="scale-95 opacity-0"
        x-transition:leave-end="scale-95 opacity-0"
        <?php if($attributes->has('wire:key')): ?>
            wire:ignore.self
            wire:key="<?php echo e($attributes->get('wire:key')); ?>.panel"
        <?php endif; ?>
        <?php if($maxHeight): ?>
            style="max-height: <?php echo e($maxHeight); ?>"
        <?php endif; ?>
        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'filament-dropdown-panel absolute z-10 w-full divide-y divide-gray-100 rounded-lg bg-white shadow-lg ring-1 ring-black/5 transition',
            'dark:divide-gray-700 dark:bg-gray-800 dark:ring-white/10' => $darkMode,
            match ($width) {
                'xs' => 'max-w-xs',
                'sm' => 'max-w-sm',
                'md' => 'max-w-md',
                'lg' => 'max-w-lg',
                'xl' => 'max-w-xl',
                '2xl' => 'max-w-2xl',
                '3xl' => 'max-w-3xl',
                '4xl' => 'max-w-4xl',
                '5xl' => 'max-w-5xl',
                '6xl' => 'max-w-6xl',
                '7xl' => 'max-w-7xl',
                default => 'max-w-[14rem]',
            },
            'overflow-y-auto' => $maxHeight,
        ]) ?>"
    >
        <?php echo e($slot); ?>

    </div>
</div>
<?php /**PATH G:\php\laravel\myapp\vendor\filament\support\src\/../resources/views/components/dropdown/index.blade.php ENDPATH**/ ?>