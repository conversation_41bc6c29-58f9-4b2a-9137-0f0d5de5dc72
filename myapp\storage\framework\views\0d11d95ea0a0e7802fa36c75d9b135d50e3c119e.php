<?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.widget','data' => ['class' => 'filament-filament-info-widget']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'filament-filament-info-widget']); ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.card.index','data' => ['class' => 'relative']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'relative']); ?>
        <div
            class="relative flex h-12 flex-col items-center justify-center space-y-2"
        >
            <div class="space-y-1">
                <a
                    href="https://filamentphp.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        'flex items-end space-x-2 text-gray-800 transition hover:text-primary-500 rtl:space-x-reverse',
                        'dark:text-primary-500 dark:hover:text-primary-400' => config('filament.dark_mode'),
                    ]) ?>"
                >
                    <svg
                        role="img"
                        aria-label="Filament"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 303 61"
                        class="h-6 w-24 fill-current"
                    >
                        <path
                            d="M11.739,58.266 C12.337,55.484 12.909,52.793 13.455,50.193 C13.91,48.0263333 14.4191667,45.6249444 14.9825,42.9888333 L15.327,41.379 L18.1126496,28.119 L28.782,28.119 L30.654,19.344 L19.5,19.344 C19.578,18.954 19.695,18.3755 19.851,17.6085 C20.007,16.8415 20.137,16.198 20.241,15.678 C20.657,13.676 21.411,12.1745 22.503,11.1735 C23.595,10.1725 25.103,9.672 27.027,9.672 C28.119,9.672 29.198,9.802 30.264,10.062 C31.33,10.322 32.344,10.686 33.306,11.154 L36.114,1.794 C35.282,1.43 34.3135,1.1115 33.2085,0.8385 C32.1035,0.5655 30.94,0.3575 29.718,0.2145 C28.496,0.0715 27.274,-1.42108547e-14 26.052,-1.42108547e-14 C24.128,-1.42108547e-14 22.282,0.2275 20.514,0.6825 C18.746,1.1375 17.134,1.9045 15.678,2.9835 C14.222,4.0625 12.9545,5.525 11.8755,7.371 C10.7965,9.217 9.971,11.518 9.399,14.274 C9.035,16.016 8.6385,17.9075 8.2095,19.9485 L8.33664956,19.344 L1.872,19.344 L1.77635684e-14,28.119 L6.47364956,28.119 L6.37884,28.56594 C6.2062,29.3761 6.03564,30.17794 5.86716,30.97146 L5.616,32.1555 C5.2,34.1185 4.823,35.906 4.485,37.518 L3.666,41.418 C2.99,44.642 2.3725,47.5605 1.8135,50.1735 C1.2545,52.7865 0.689,55.484 0.117,58.266 L11.739,58.266 Z"
                        ></path>
                        <path
                            d="M11.739 58.266C12.337 55.484 12.909 52.793 13.455 50.193 13.91 48.0263333 14.4191667 45.6249444 14.9825 42.9888333L15.327 41.379 18.1126496 28.119 28.782 28.119 30.654 19.344 19.5 19.344C19.578 18.954 19.695 18.3755 19.851 17.6085 20.007 16.8415 20.137 16.198 20.241 15.678 20.657 13.676 21.411 12.1745 22.503 11.1735 23.595 10.1725 25.103 9.672 27.027 9.672 28.119 9.672 29.198 9.802 30.264 10.062 31.33 10.322 32.344 10.686 33.306 11.154L36.114 1.794C35.282 1.43 34.3135 1.1115 33.2085.8385 32.1035.5655 30.94.3575 29.718.2145 28.496.0715 27.274-1.42108547e-14 26.052-1.42108547e-14 24.128-1.42108547e-14 22.282.2275 20.514.6825 18.746 1.1375 17.134 1.9045 15.678 2.9835 14.222 4.0625 12.9545 5.525 11.8755 7.371 10.7965 9.217 9.971 11.518 9.399 14.274 9.035 16.016 8.6385 17.9075 8.2095 19.9485L8.33664956 19.344 1.872 19.344 1.77635684e-14 28.119 6.47364956 28.119 6.37884 28.56594C6.2062 29.3761 6.03564 30.17794 5.86716 30.97146L5.616 32.1555C5.2 34.1185 4.823 35.906 4.485 37.518L3.666 41.418C2.99 44.642 2.3725 47.5605 1.8135 50.1735 1.2545 52.7865.689 55.484.117 58.266L11.739 58.266zM62.3193768 58.266C62.8913768 55.484 63.4503768 52.793 63.9963768 50.193 64.5423768 47.593 65.1663768 44.655 65.8683768 41.379L70.4313768 19.773C71.1853768 16.185 71.9068768 12.7725 72.5958768 9.5355 73.2848768 6.2985 73.9543768 3.146 74.6043768.078L62.7483768 1.092C62.0983768 4.212 61.4483768 7.319 60.7983768 10.413 60.1483768 13.507 59.4723768 16.64 58.7703768 19.812L54.2463768 41.418C53.5443768 44.642 52.9203768 47.5605 52.3743768 50.1735 51.8283768 52.7865 51.2563768 55.484 50.6583768 58.266L62.3193768 58.266z"
                        ></path>
                        <path
                            d="M38.7656433,19.4978519 L38.0777659,22.021842 L38.0610693,22.131033 C38.0251643,22.4950191 38.1846143,22.8536527 38.4749965,23.0415561 L42.5405456,25.6727365 L37.8469335,26.0597897 C37.4781717,26.0902123 37.1701788,26.3833635 37.0869964,26.7831066 L36.7567399,28.3701948 L36.7400433,28.4793858 C36.7041382,28.8433719 36.8635882,29.2020055 37.1539704,29.3899089 L41.2194156,32.0208772 L36.5259074,32.4081426 C36.1571456,32.4385651 35.8491527,32.7317163 35.7659703,33.1314594 L35.4357138,34.7185476 L35.4190172,34.8277387 C35.3831121,35.1917248 35.5425621,35.5503584 35.8329443,35.7382617 L39.8982856,38.3690178 L35.2048813,38.7564954 C34.8361195,38.7869179 34.5281266,39.0800691 34.4449442,39.4798122 L34.1146877,41.0669004 L34.0979911,41.1760915 C34.062086,41.5400776 34.221536,41.8987112 34.5119182,42.0866145 L38.5771556,44.7171584 L33.8838552,45.1048482 C33.5150934,45.1352707 33.2071005,45.4284219 33.1239181,45.8281651 L32.7936616,47.4152533 L32.776965,47.5244443 C32.7410599,47.8884304 32.90051,48.247064 33.1908921,48.4349674 L37.2568698,51.0652991 L32.5628291,51.453201 C32.1940673,51.4836235 31.8860744,51.7767748 31.802892,52.1765179 L30.2847167,58.4347902 L35.5467639,58.3429377 L36.3076904,54.9378526 L42.8887216,54.2623324 C43.2574834,54.2319099 43.5654763,53.9387586 43.6486587,53.5390155 L43.9789152,51.9519273 L43.9956118,51.8427363 C44.0315169,51.4787502 43.8720669,51.1201166 43.5816847,50.9322132 L39.5158755,48.3005143 L44.2097477,47.9139796 C44.5785095,47.883557 44.8865024,47.5904058 44.9696848,47.1906627 L45.2999413,45.6035745 L45.3166379,45.4943835 C45.352543,45.1303974 45.193093,44.7717638 44.9027108,44.5838604 L40.8370055,41.9523737 L45.5307738,41.5656268 C45.8995356,41.5352042 46.2075285,41.242053 46.2907109,40.8423099 L46.6209674,39.2552217 L46.637664,39.1460307 C46.6735691,38.7820446 46.5141191,38.423411 46.2237369,38.2355076 L42.1572913,35.604233 L46.8517999,35.217274 C47.2205617,35.1868514 47.5285546,34.8937002 47.611737,34.4939571 L47.9419935,32.9068689 L47.9586901,32.7976779 C47.9945952,32.4336918 47.8351451,32.0750582 47.544763,31.8871548 L43.4784213,29.2551558 L48.172826,28.8689211 C48.5415878,28.8384986 48.8495807,28.5453474 48.9327631,28.1456043 L49.2630196,26.5585161 L49.2797162,26.449325 C49.3156213,26.0853389 49.1561712,25.7267053 48.8657891,25.538802 L43.4784213,22.021842 L44.5654245,19.1812071 L38.7656433,19.4978519 Z"
                            transform="rotate(1 39.785 38.808)"
                        ></path>
                        <path
                            d="M46.3029925 13.338C48.4869925 13.338 50.3134925 12.9415 51.7824925 12.1485 53.2514925 11.3555 54.2199925 9.828 54.6879925 7.566 55.1039925 5.59 54.7919925 3.8935 53.7519925 2.4765 52.7119925 1.0595 51.1129925.351 48.9549925.351 46.7969925.351 44.9704925.806 43.4754925 1.716 41.9804925 2.626 41.0119925 4.121 40.5699925 6.201 40.1019925 8.489 40.4074925 10.2505 41.4864925 11.4855 42.5654925 12.7205 44.1709925 13.338 46.3029925 13.338zM94.7512855 18.291C98.4952855 18.291 101.576286 18.915 103.994286 20.163 106.412286 21.411 108.089286 23.1855 109.025286 25.4865 109.961286 27.7875 110.078286 30.524 109.376286 33.696 109.116286 34.996 108.843286 36.3155 108.557286 37.6545 108.271286 38.9935 107.972286 40.391 107.660286 41.847L107.660286 41.847 106.958286 45.123C106.542286 47.073 106.106786 49.127 105.651786 51.285 105.196786 53.443 104.696286 55.77 104.150286 58.266L104.150286 58.266 94.0882855 58.266 94.5952855 53.079 93.6982855 53.079C92.7362855 54.457 91.6767855 55.5945 90.5197855 56.4915 89.3627855 57.3885 88.1082855 58.0645 86.7562855 58.5195 85.4042855 58.9745 83.9482855 59.202 82.3882855 59.202 79.9182855 59.202 77.8512855 58.5975 76.1872855 57.3885 74.5232855 56.1795 73.3662855 54.574 72.7162855 52.572 72.0662855 50.57 72.0012855 48.386 72.5212855 46.02 72.9112855 44.2 73.5417855 42.679 74.4127855 41.457 75.2837855 40.235 76.3367855 39.234 77.5717855 38.454 78.8067855 37.674 80.1717855 37.0565 81.6667855 36.6015 83.1617855 36.1465 84.7152855 35.802 86.3272855 35.568L86.3272855 35.568 98.5342855 33.657C98.7942855 32.487 98.6967855 31.4665 98.2417855 30.5955 97.7867855 29.7245 96.9742855 29.0485 95.8042855 28.5675 94.6342855 28.0865 93.1392855 27.846 91.3192855 27.846 90.3832855 27.846 89.3887855 27.9175 88.3357855 28.0605 87.2827855 28.2035 86.1842855 28.4115 85.0402855 28.6845 83.8962855 28.9575 82.7197855 29.2955 81.5107855 29.6985 80.3017855 30.1015 79.0602855 30.576 77.7862855 31.122L77.7862855 31.122 79.6192855 20.904C80.5552855 20.592 81.6212855 20.2865 82.8172855 19.9875 84.0132855 19.6885 85.2742855 19.409 86.6002855 19.149 87.9262855 18.889 89.2847855 18.681 90.6757855 18.525 92.0667855 18.369 93.4252855 18.291 94.7512855 18.291zM97.0912855 38.922C96.7272855 39.182 96.2852855 39.4355 95.7652855 39.6825 95.2452855 39.9295 94.5302855 40.1765 93.6202855 40.4235 92.7102855 40.6705 91.4752855 40.963 89.9152855 41.301 88.9012855 41.535 87.9782855 41.808 87.1462855 42.12 86.3142855 42.432 85.6252855 42.874 85.0792855 43.446 84.5332855 44.018 84.1692855 44.759 83.9872855 45.669 83.7272855 47.099 83.9742855 48.2105 84.7282855 49.0035 85.4822855 49.7965 86.5092855 50.193 87.8092855 50.193 88.7452855 50.193 89.6877855 49.9915 90.6367855 49.5885 91.5857855 49.1855 92.4957855 48.607 93.3667855 47.853 94.2377855 47.099 95.0372855 46.202 95.7652855 45.162L95.7652855 45.162zM153.787258 25.2364649C153.377677 23.2794277 152.545046 21.7182727 151.289364 20.553 149.664364 19.045 147.395864 18.291 144.483864 18.291 142.689864 18.291 140.967364 18.603 139.316364 19.227 137.665364 19.851 136.144364 20.7025 134.753364 21.7815 133.362364 22.8605 132.185864 24.063 131.223864 25.389L130.248864 25.389 131.067864 18.759 121.239864 19.344C120.563864 22.594 119.900864 25.7335 119.250864 28.7625 118.600864 31.7915 117.989864 34.71 117.417864 37.518L116.598864 41.418C115.896864 44.642 115.272864 47.5605 114.726864 50.1735 114.180864 52.7865 113.608864 55.484 113.010864 58.266L124.671864 58.266C125.243864 55.484 125.802864 52.806 126.348864 50.232 126.894864 47.658 127.479864 44.915 128.103864 42.003L129.780864 33.93C130.612864 32.968 131.451364 32.11 132.296364 31.356 133.141364 30.602 134.025364 30.004 134.948364 29.562 135.871364 29.12 136.852864 28.899 137.892864 28.899 139.712864 28.899 140.876364 29.6075 141.383364 31.0245 141.890364 32.4415 141.896864 34.32 141.402864 36.66L140.271864 42.042C139.647864 44.954 139.075864 47.684 138.555864 50.232 138.035864 52.78 137.476864 55.458 136.878864 58.266L148.500864 58.266C149.098864 55.484 149.677364 52.7865 150.236364 50.1735 150.795364 47.5605 151.412864 44.642 152.088864 41.418 152.400864 39.91 152.706364 38.454 153.005364 37.05 153.304364 35.646 153.570864 34.398 153.804864 33.306L153.74965 33.56 153.831948 33.4695833C154.389503 32.8658056 154.952836 32.3024722 155.521948 31.7795833L155.949864 31.395C156.807864 30.641 157.698364 30.0365 158.621364 29.5815 159.544364 29.1265 160.525864 28.899 161.565864 28.899 163.385864 28.899 164.549364 29.6075 165.056364 31.0245 165.563364 32.4415 165.569864 34.32 165.075864 36.66L163.944864 42.042C163.346864 44.98 162.781364 47.7165 162.248364 50.2515 161.715364 52.7865 161.149864 55.458 160.551864 58.266L172.212864 58.266C172.784864 55.484 173.343864 52.793 173.889864 50.193 174.435864 47.593 175.059864 44.668 175.761864 41.418 176.073864 39.91 176.379364 38.454 176.678364 37.05 176.977364 35.646 177.243864 34.398 177.477864 33.306 178.465864 28.756 178.153864 25.116 176.541864 22.386 174.929864 19.656 172.134864 18.291 168.156864 18.291 166.362864 18.291 164.633864 18.6095 162.969864 19.2465 161.305864 19.8835 159.765364 20.748 158.348364 21.84 156.931364 22.932 155.715864 24.167 154.701864 25.545L153.84665 25.545 153.787258 25.2364649zM204.202305 18.291C208.284305 18.291 211.612305 19.032 214.186305 20.514 216.760305 21.996 218.502305 24.076 219.412305 26.754 220.322305 29.432 220.335305 32.552 219.451305 36.114 219.243305 36.972 219.028805 37.778 218.807805 38.532 218.642055 39.0975 218.465336 39.648375 218.277648 40.184625L218.277648 40.184625 218.086305 40.716 194.21065 40.716 194.203138 40.8698333C194.148249 42.3576111 194.367805 43.6518333 194.861805 44.7525 195.602805 46.4035 196.831305 47.6385 198.547305 48.4575 200.263305 49.2765 202.369305 49.686 204.865305 49.686 205.957305 49.686 207.172805 49.5885 208.511805 49.3935 209.850805 49.1985 211.183305 48.8995 212.509305 48.4965 213.835305 48.0935 215.057305 47.58 216.175305 46.956L216.175305 46.956 214.615305 56.862C213.913305 57.278 212.951305 57.668 211.729305 58.032 210.507305 58.396 209.096805 58.695 207.497805 58.929 205.898805 59.163 204.176305 59.28 202.330305 59.28 197.650305 59.28 193.743805 58.3765 190.610805 56.5695 187.477805 54.7625 185.267805 52.195 183.980805 48.867 182.693805 45.539 182.518305 41.587 183.454305 37.011 184.234305 33.241 185.560305 29.952 187.432305 27.144 189.304305 24.336 191.657305 22.1585 194.491305 20.6115 197.325305 19.0645 200.562305 18.291 204.202305 18.291zM204.436305 26.52C203.110305 26.52 201.816805 26.871 200.555805 27.573 199.294805 28.275 198.170305 29.367 197.182305 30.849 196.589505 31.7382 196.080945 32.78184 195.656625 33.97992L195.656625 33.97992 195.53965 34.322 209.63365 34.028 209.678078 33.6929751C209.871457 31.9817285 209.619199 30.4487368 208.921305 29.094 208.037305 27.378 206.542305 26.52 204.436305 26.52zM233.818831 58.266C234.390831 55.484 234.949831 52.8125 235.495831 50.2515 236.041831 47.6905 236.626831 44.941 237.250831 42.003L238.927831 33.93C239.759831 32.968 240.611331 32.11 241.482331 31.356 242.353331 30.602 243.263331 30.004 244.212331 29.562 245.161331 29.12 246.194831 28.899 247.312831 28.899 249.366831 28.899 250.666831 29.6075 251.212831 31.0245 251.758831 32.4415 251.771831 34.32 251.251831 36.66L250.120831 42.042C249.522831 44.954 248.957331 47.684 248.424331 50.232 247.891331 52.78 247.325831 55.458 246.727831 58.266L258.388831 58.266C258.986831 55.484 259.565331 52.7865 260.124331 50.1735 260.683331 47.5605 261.287831 44.642 261.937831 41.418 262.275831 39.91 262.594331 38.454 262.893331 37.05 263.192331 35.646 263.458831 34.398 263.692831 33.306 264.654831 28.756 264.316831 25.116 262.678831 22.386 261.040831 19.656 258.115831 18.291 253.903831 18.291 252.057831 18.291 250.283331 18.603 248.580331 19.227 246.877331 19.851 245.323831 20.7025 243.919831 21.7815 242.515831 22.8605 241.332831 24.063 240.370831 25.389L239.395831 25.389 240.214831 18.759 230.386831 19.344C229.710831 22.594 229.047831 25.74 228.397831 28.782 227.747831 31.824 227.136831 34.736 226.564831 37.518L225.745831 41.418C225.069831 44.616 224.452331 47.528 223.893331 50.154 223.334331 52.78 222.755831 55.484 222.157831 58.266L233.818831 58.266zM285.401439 59.319C286.337439 59.319 287.351439 59.2345 288.443439 59.0655 289.535439 58.8965 290.555939 58.6755 291.504939 58.4025 292.453939 58.1295 293.201439 57.811 293.747439 57.447L294.995439 47.892C293.981439 48.594 292.973939 49.088 291.972939 49.374 290.971939 49.66 290.055439 49.803 289.223439 49.803 287.325439 49.803 286.031939 49.153 285.342939 47.853 284.653939 46.553 284.582439 44.655 285.128439 42.159L288.07365 28.119 299.675439 28.119 301.547439 19.344 289.92965 19.344 290.008314 18.978375C290.176502 18.1910625 290.338291 17.4302578 290.493681 16.6959609L290.646939 15.9705C291.049939 14.0595 291.446439 12.2005 291.836439 10.3935 292.226439 8.5865 292.629439 6.669 293.045439 4.641L281.111439 6.708C280.565439 9.282 280.038939 11.778 279.531939 14.196 279.227739 15.6468 278.897799 17.19588 278.542119 18.84324L278.43365 19.344 272.141439 19.344 270.269439 28.119 276.58165 28.119 276.533032 28.3549259 276.392439 29.0355C275.976439 31.0505 275.579939 32.9355 275.202939 34.6905 274.825939 36.4455 274.468439 38.1355 274.130439 39.7605 273.792439 41.3855 273.441439 43.043 273.077439 44.733 272.375439 48.035 272.453439 50.765 273.311439 52.923 274.169439 55.081 275.644939 56.6865 277.737939 57.7395 279.830939 58.7925 282.385439 59.319 285.401439 59.319z"
                        ></path>
                    </svg>
                </a>
            </div>

            <div class="flex space-x-2 text-sm rtl:space-x-reverse">
                <a
                    href="https://filamentphp.com/docs"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        'text-gray-600 outline-none hover:text-primary-500 focus:underline',
                        'dark:text-gray-300 dark:hover:text-primary-500' => config('filament.dark_mode'),
                    ]) ?>"
                >
                    <?php echo e(__('filament::widgets/filament-info-widget.buttons.visit_documentation.label')); ?>

                </a>

                <span>&bull;</span>

                <a
                    href="https://github.com/filamentphp/filament"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                        'text-gray-600 outline-none hover:text-primary-500 focus:underline',
                        'dark:text-gray-300 dark:hover:text-primary-500' => config('filament.dark_mode'),
                    ]) ?>"
                >
                    <?php echo e(__('filament::widgets/filament-info-widget.buttons.visit_github.label')); ?>

                </a>
            </div>
        </div>

        <svg
            class="absolute bottom-0 right-0 h-16 w-16 rtl:left-0 rtl:right-auto"
            xmlns="http://www.w3.org/2000/svg"
            width="300.000000pt"
            height="418.000000pt"
            viewBox="0 0 300.000000 418.000000"
            preserveAspectRatio="xMidYMid meet"
        >
            <g
                class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                    'dark:fill-current' => config('filament.dark_mode'),
                ]) ?>"
                transform="translate(0 418) scale(.1 -.1)"
                fill="#111827"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="m2735 4138c-50-59-87-174-102-313-13-135-22-170-49-193l-27-23 27-28c23-24 26-35 26-102 0-42-3-78-6-82-4-3-22 5-40 20-19 14-34 23-34 20s14-31 30-62c35-66 35-74 13-206-17-106-16-104-66-84-18 7-17 5 2-34 12-22 22-58 23-79 2-34-9-147-18-197-2-11-4-51-5-88l-2-69-53 53c-30 29-54 50-54 47 0-4 23-45 51-92l52-86-7-87c-4-48-10-91-12-95-3-5-25 5-49 22-24 16-46 30-50 30-3 0 18-30 46-67l51-67-4-66c-3-36-14-84-25-107-17-34-18-44-8-53 33-28 23-119-15-140-10-6-57-10-104-10h-85l-3 40c-4 49-22 49-26 0-2-19-5-36-7-38-6-7-186-24-200-18-9 3-15 18-15 35 0 16-5 33-11 36-17 11-20 4-28-71 0-6-31-16-68-22-38-6-82-14-99-18-30-6-33-4-44 32-19 60-30 64-30 11 0-58-11-64-138-74l-87-6 43 46c23 26 42 55 42 65 0 30 37 69 77 83 32 10 35 13 20 24-23 17-22 20 18 81 19 29 44 73 55 98l19 46-27-6c-65-16-137-49-245-114-65-38-117-64-117-59s12 58 26 117c26 107 30 114 92 162l27 21-37-3c-41-4-42-3-28 57 12 56 19 92 25 150 6 46 4 51-14 52-13 1-35-16-62-48-39-46-43-49-59-34s-18 13-24-27c-9-59-60-145-100-169-19-11-41-36-51-58-9-21-28-44-41-50s-24-19-25-28c-2-38-10-73-42-169-33-98-34-99-68-102s-35-2-48 48c-18 68-61 113-130 134-90 27-159 16-203-31-42-45-53-95-34-153 9-27 16-51 16-53 0-3-33-8-72-12-112-11-450-105-480-134-8-7 30-154 55-210l14-34 44 62 43 61 52-51 51-51 22 36 22 35 72-80-24-27c-13-15-27-31-30-35-4-5-20-2-35 6-16 8-42 17-59 18-27 4-31 0-46-41-10-25-20-53-23-62-4-15-13-14-86 13-44 16-85 29-91 29-7 0-9-38-7-108l4-107h34c43 1 198 13 225 19 26 5 77 12 284 42l104 14 29-27 28-27v27c0 33 29 47 95 47 32 0 51-7 74-26l31-26v41c0 48 8 51 45 14 48-48 65-98 65-195 0-52 6-103 14-125 16-42 23-113 32-338 4-98 13-174 25-220 32-116 27-144-32-212-76-87-78-93-29-94 22-1 46-2 53-3 6 0 14-20 17-43l5-41 30 24 30 23 36-35 36-36 13 34c7 21 13 112 15 248 2 118 6 267 9 330s8 183 12 265c3 83 8 151 9 153 2 2 62 14 135 27l131 23-7-67c-3-36-8-68-9-71-2-3-6-37-10-75-4-39-9-77-10-85-2-8-6-71-10-140-7-119-12-141-53-223-2-4 8-5 23-1l28 7-5-90c-4-85-6-93-51-168-26-44-47-89-47-101s-3-29-6-37c-5-13-1-13 33-2 24 9 75 13 139 12l100-2 3 110c1 61 3 124 5 140 2 17 7 95 10 175 9 197 15 260 32 360 9 47 18 105 21 128 3 24 11 45 17 47s61 6 121 10c61 3 129 8 151 11 43 5 60-8 101-72 9-16 11-10 6 32-5 47-4 51 18 57 52 14 104 7 143-19 22-15 37-21 34-13-4 8-9 22-13 31-5 14 2 18 42 23 26 4 56 9 66 12 17 4 18 0 11-55-4-39-1-82 9-128 9-38 18-118 22-178s13-143 21-184c17-89 19-265 3-295-7-12-31-43-55-69s-44-51-44-57c0-5-3-16-6-24-9-26 31-29 87-7 64 24 59 23 59 7 0-14 123-52 139-43 20 13 22 65 4 127-14 50-18 107-19 259-1 107-5 281-10 386l-7 192 22 12c13 6 24 10 26 8 1-2 6-70 10-153 4-82 8-161 10-175 1-14 6-61 9-105 4-44 19-132 32-195 25-114 26-163 7-282-6-41-4-43 45-29 18 6 63 11 100 12s70 4 73 5c5 3-4 105-15 179-3 19-8 70-11 113-3 42-15 111-27 152-12 45-21 103-20 145 0 39-6 90-13 115-8 25-16 58-20 74-6 28-5 28 24 23 17-4 31-5 31-3s-12 25-26 51l-27 47 8 577c4 317 9 587 10 601 2 14 4 29 4 34 1 5 18 24 38 43l36 35-39-3-39-4v101c1 84 4 106 22 136l21 36-25 7-25 6 8 82c16 161 7 497-17 631-19 111-24 191-13 221 4 8 26 29 51 46 52 36 52 46 0 24-63-26-63-25-72 88-5 56-9 131-10 167s-6 74-12 85c-17 31-41 124-48 185-10 81-17 88-50 48zm38-213c1-44-1-92-6-107-7-25-5-28 18-28s25-3 25-48c0-31-9-65-24-96l-24-46 66 12-10-46c-7-35-6-58 7-101 8-30 21-101 27-158 9-85 8-108-4-137-13-32-13-39 5-79 12-27 21-77 25-128l5-83-39-36-38-37 37 5 37 4v-124c0-126-5-148-43-189l-20-23h31 32v-87c0-67-5-99-21-133-11-24-16-46-12-47 28-10 29-26 21-338-5-176-12-489-16-695l-8-375 28-115c16-63 35-168 43-233 14-113 40-272 51-310 5-16 1-17-33-11-21 4-51 13-66 21-26 13-31 13-47-2-18-17-18-16-3 33 15 48 15 56-2 131l-18 80 25 58c25 57 45 133 31 120-3-4-21-33-39-65-19-31-34-52-34-47 0 6-5 53-10 105-6 52-14 184-19 293s-11 200-14 203c-2 3-23-3-46-12-67-27-75-53-67-213 4-74 9-162 13-194l6-60-27 7c-16 4-26 3-24-1 38-74 41-93 44-218 3-111 18-228 40-299 5-14-8-8-52 23-31 22-61 41-67 41-5 0-31-11-59-25-27-14-53-25-56-25-4 0 20 26 51 58 49 49 58 65 60 98 4 85-15 404-30 484-8 47-16 137-17 200s-8 128-14 143l-12 29-75-7c-72-7-74-7-100 21-22 23-28 26-35 14-6-8-10-18-10-22 0-13-44-36-82-42-36-7-37-6-49 34-7 22-15 40-19 40s-11-20-14-44l-7-44-72-7c-40-4-101-5-137-3-62 3-65 4-85 39l-20 37-23-74c-12-40-29-107-37-150-8-42-23-91-34-109-15-26-17-41-10-76 10-53 11-150 3-202-6-32-10-37-27-31-15 5-19 3-14-9 21-55 27-110 24-238l-2-145-35 13c-54 19-82 15-133-17-26-16-47-27-47-25 0 3 21 37 46 75 55 82 63 116 64 275 0 77 7 153 19 213 14 65 18 113 14 165-5 66-3 79 21 126 31 63 31 61 7 61-17 0-18 5-11 38 4 20 10 52 14 70l6 32h-50c-47 0-50 2-50 25 0 14-4 25-10 25-5 0-10-4-10-9 0-15-66-51-137-74-75-25-96-47-115-119-10-38-10-60 1-109 12-60 12-63-12-86l-24-24 20-57c16-47 18-70 11-127-10-93-11-309-2-414l8-84-23 20c-28 26-92 59-133 67l-32 7 45 42c29 28 45 51 45 67 1 74-20 338-27 350-4 7-12 77-17 154-5 78-11 164-14 192-4 37-1 60 10 82 21 40 20 42-7 42-21 0-24 7-35 73-19 111-42 150-110 184l-57 29-40-23c-26-15-57-23-87-23-26 0-93-9-148-20-85-16-263-40-482-65-27-3-48-3-48 0 0 15 418 192 590 250 129 44 217 86 199 95-17 9-147 26-319 40-245 21-426 55-479 89-9 6 10 18 67 40 103 39 162 52 241 53 41 0 80 7 109 20 49 22 401 85 494 89l56 2 17 64c9 34 33 89 52 120 20 32 42 73 49 91 8 18 25 37 39 42 17 6 26 20 31 45 4 22 41 82 101 162 52 71 97 130 100 133 11 12-11-100-33-161-13-37-24-81-24-99v-32l-35 21c-41 24-44 15-10-35 30-43 30-50 9-165-9-49-14-92-11-95s54 25 114 62c111 69 243 137 251 129 4-4-37-60-89-122-55-65-169-286-155-301 3-2 55 1 116 7 96 9 116 8 141-5 28-14 31-14 59 10 31 25 69 40 133 51 33 5 38 3 48-19l12-25 34 29c33 30 36 30 147 30 62 0 133 3 158 6l45 6 13-46c15-54 30-60 30-11 0 19 6 39 13 44 80 64 87 74 97 151 5 41 10 117 10 169v94l34-6 34-5-30 48-30 49 11 173c21 298 29 364 49 411 14 32 18 58 15 90-3 26 4 101 15 169s23 165 27 217c3 52 12 106 20 121 12 23 12 33-2 71-13 38-14 58-3 139 17 139 38 235 68 309l26 66 14-55c7-30 14-91 15-135zm34-92c-3-10-5-4-5 12 0 17 2 24 5 18 2-7 2-21 0-30zm-181-238c1-8-2-13-7-9-5 3-9 12-9 21 0 19 13 9 16-12zm229-74c3-5 1-12-5-16-5-3-10 1-10 9 0 18 6 21 15 7zm-298-513c-3-7-5-2-5 12s2 19 5 13c2-7 2-19 0-25zm335-500c-7-7-12-8-12-2 0 14 12 26 19 19 2-3-1-11-7-17zm-1462-169c0-5-4-9-10-9-5 0-10 7-10 16 0 8 5 12 10 9 6-3 10-10 10-16zm1078-47c-3-14-6-14-16 6-7 12-10 27-6 33 9 15 27-16 22-39zm-1298-12c-10-19-30-36-30-26 0 16 24 55 31 50 5-3 5-14-1-24zm1681-53c-8-8-11-7-11 4 0 20 13 34 18 19 3-7-1-17-7-23zm-1748-22c-3-9-8-14-10-11-3 3-2 9 2 15 9 16 15 13 8-4zm-329-119c18-8 43-24 55-37 23-24 65-98 59-104-4-4-300-55-318-55-9 0-11 21-8 76 3 71 6 78 36 105 27 25 41 29 87 29 31 0 71-6 89-14zm810-55c3-5-1-11-9-14-9-4-12-1-8 9 6 16 10 17 17 5zm366-169c0-13-12-22-22-16s-1 24 13 24c5 0 9-4 9-8zm-243-58c-3-4-14-3-24 0-15 6-15 8-3 16 16 10 39-4 27-16zm-1567-164c0-3-15-23-34-45-32-37-34-38-44-18-6 11-14 38-18 60l-6 40 51-16c28-9 51-18 51-21zm139-20h26l-27-26-26-26-31 23c-40 29-40 43 0 35 17-3 43-6 58-6zm174-24c15-2 46-3 69-4l42-2-22-22c-21-22-23-22-51-5-39 23-50 22-80-7l-26-25-34 41-35 41 55-8c30-4 66-8 82-9zm235-22c28-4 52-10 52-14 0-12-163-60-220-65-30-2-71-9-90-15-19-5-36-10-38-10-4 0 37 70 48 83 3 4 24-5 45-21l39-28 37 38c38 40 43 41 127 32zm-269-124c15 0 21-4 16-9-9-9-259-111-270-111-3 0-1 8 5 18 5 9 16 34 25 56 16 41 8 40 103 20 21-4 33 0 51 20 17 18 28 23 35 16 5-5 21-10 35-10zm-280-126c5-5 5-5-51-37-26-15-50-36-53-48-14-43-30-12-33 64l-3 76 68-25c37-14 70-28 72-30zm721-9c0-8-4-15-9-15-13 0-22 16-14 24 11 11 23 6 23-9zm1409-239c7-9 8-17 2-20-14-9-41 4-41 20 0 18 24 18 39 0zm-1242-48c-3-7-5-2-5 12s2 19 5 13c2-7 2-19 0-25zm1063 11c0-5-7-9-15-9-15 0-20 12-9 23 8 8 24-1 24-14zm730-919c0-5-15-10-32-9-28 0-30 2-13 9 28 12 45 12 45 0zm-1705-30c3-5 2-10-4-10-5 0-13 5-16 10-3 6-2 10 4 10 5 0 13-4 16-10zm412-16c-3-3-12-4-19-1-8 3-5 6 6 6 11 1 17-2 13-5z"
                />
                <path
                    d="m2746 3379c-3-17-6-60-6-95 0-59 2-64 21-64 21 0 22 3 15 88-8 94-19 122-30 71z"
                />
                <path
                    d="m2706 2889c-8-44-6-130 4-154 6-13 9 16 9 83 1 56-1 102-3 102s-6-14-10-31z"
                />
                <path
                    d="m2741 2590c0-30 6-73 13-95l12-40 8 33c9 38 0 97-19 132-14 24-14 22-14-30z"
                />
                <path
                    d="m2610 2562c0-5 13-27 29-50 34-46 58-66 48-38-4 10-7 21-7 26s-12 22-26 39c-25 30-44 40-44 23z"
                />
                <path
                    d="m2732 2310c-11-18-9-26 14-58l26-37-7 45c-10 63-18 74-33 50z"
                />
                <path
                    d="m2640 2305c0-4 12-30 28-58 28-53 46-43 18 11-15 29-46 61-46 47z"
                />
                <path
                    d="m2665 2079c8-39 63-164 70-158 7 8-20 111-38 146-25 47-41 53-32 12z"
                />
                <path
                    d="m1186 2041-26-51h24c21 0 25 6 31 46 3 25 4 48 1 51-2 3-16-18-30-46z"
                />
                <path
                    d="m2545 1963c3-26 9-61 12-78l6-30 8 25c4 14 7 44 8 67 1 33-3 45-20 53-20 11-20 10-14-37z"
                />
                <path
                    d="m1017 1828c-49-47-80-113-73-153 16-84 131-123 209-70 94 65 93 188-4 237-53 28-92 23-132-14zm141-28c49-46 33-131-31-170-40-25-64-25-104 0-56 34-53 96 7 160 35 37 94 41 128 10z"
                />
                <path
                    d="m1074 1754c3-9 6-24 6-35 0-24 9-24 39 2 23 19 23 20 5 34-26 20-58 20-50-1z"
                />
                <path
                    d="m688 1791c18-17 41-31 51-31 11 0 27-3 37-7 12-4 15-3 10 5-8 14-69 46-106 56-24 7-23 6 8-23z"
                />
                <path
                    d="m2675 1761c22-10 51-21 65-25l25-7-28 25c-20 19-39 26-65 25h-37l40-18z"
                />
                <path
                    d="m1496 1715c-3-9-6-30-6-46 0-24 4-29 24-29 23 0 24 0 9 42-18 52-19 53-27 33z"
                />
                <path
                    d="m2310 1712c0-37 58-132 82-132 10 0-33 85-61 120-11 14-21 19-21 12z"
                />
                <path
                    d="m1652 1680c9-29 77-95 86-85 10 10-72 115-89 115-5 0-3-14 3-30z"
                />
                <path
                    d="m1320 1684c0-14 71-116 77-111 8 9-27 73-53 98-13 12-24 18-24 13z"
                />
                <path
                    d="m2174 1668c12-54 25-75 60-96 20-12 36-19 36-16 0 8-48 78-75 109l-26 30 5-27z"
                />
                <path
                    d="m2e3 1624c5-33 16-55 40-77 31-31 32-31 25-7-12 41-56 130-64 130-4 0-5-21-1-46z"
                />
                <path
                    d="m2450 1644c0-17 62-133 80-149l20-18-19 55c-27 78-49 118-66 118-8 0-15-3-15-6z"
                />
                <path
                    d="m1586 1607c-21-28-56-105-56-125 0-13 44 37 60 68 18 37 34 90 26 90-3 0-16-15-30-33z"
                />
                <path
                    d="m1793 1575c3-11 11-42 18-70 18-76 30-79 27-6-3 54-7 66-27 79-22 15-23 15-18-3z"
                />
                <path
                    d="m1254 1545c4-16 19-55 34-85 23-47 27-52 30-30 6 37-15 94-44 121l-27 24 7-30z"
                />
                <path
                    d="m2722 1538c3-7 13-37 22-65 18-55 22-59 46-43 12 7 8 18-24 64-37 54-53 70-44 44z"
                />
                <path
                    d="m1671 1484c-11-15-21-38-21-53 1-22 4-19 26 22 29 56 26 71-5 31z"
                />
                <path
                    d="m2351 1446c21-35 69-75 69-56 0 13-69 90-81 90-5 0 1-15 12-34z"
                />
                <path
                    d="m1956 1452c-2-4 11-28 30-51 37-48 42-43 18 19-15 37-36 51-48 32z"
                />
                <path
                    d="m2490 1446c0-27 72-126 92-126 14 0-29 74-61 104-17 17-31 26-31 22z"
                />
                <path
                    d="m2112 1391c3-27 15-64 27-83l22-33-6 48c-7 49-31 117-41 117-4 0-4-22-2-49z"
                />
                <path
                    d="m1205 1358c-25-28-53-65-61-81-14-27-14-28 3-18 32 17 82 75 98 114 8 20 13 37 11 37s-25-23-51-52z"
                />
                <path
                    d="m1485 1363c8-42 29-88 46-99 16-9 2 50-22 99-25 47-33 47-24 0z"
                />
                <path
                    d="m2692 1325c-14-47-15-94-3-114 13-20 23 38 19 104l-3 50-13-40z"
                />
                <path
                    d="m1675 1310c12-39 32-75 51-91 15-13 16-12 9 11-12 39-32 75-51 91-15 13-16 12-9-11z"
                />
                <path
                    d="m2250 1325c0-6 63-63 94-85l21-14-19 29c-41 63-52 74-73 75-13 0-23-2-23-5z"
                />
                <path
                    d="m1347 1272c-17-19-30-50-41-97l-7-30 21 25c36 44 72 120 57 120-8 0-21-8-30-18z"
                />
                <path
                    d="m1896 1265c-10-16-16-45-15-82 1-57 1-57 14-28 13 31 30 135 22 135-3 0-12-11-21-25z"
                />
                <path
                    d="m1997 1253c-15-18-16-26-5-74l12-54 8 45c5 25 7 58 6 74-3 28-3 28-21 9z"
                />
                <path
                    d="m1216 1198c-18-28-50-128-43-135 9-8 46 63 56 107 12 53 8 60-13 28z"
                />
                <path
                    d="m2446 1201c19-18 109-70 113-65 3 2-11 20-29 39-24 25-43 35-64 35-18 0-26-4-20-9z"
                />
                <path
                    d="m712 2047-32-24 23-21c31-29 33-28 47 14 16 49 2 60-38 31z"
                />
            </g>
        </svg>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
<?php /**PATH G:\php\laravel\myapp\vendor\filament\filament\src\/../resources/views/widgets/filament-info-widget.blade.php ENDPATH**/ ?>